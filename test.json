import requests

cookies = {
    'sensorsdata2015jssdkchannel': '%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D',
    '_ga': 'GA1.1.1213731967.1750903226',
    '_uetvid': '53f09740523111f086f3674ff56e4186',
    '_clck': 'hw1dfp%7C2%7Cfx3%7C0%7C2003',
    '_gcl_au': '1.1.1316856398.1750903228',
    '_ga_57W1QW8BJG': 'GS2.1.s1750903225$o1$g0$t1750903337$j60$l0$h21324942',
    '__wpkreporterwid_': 'be6f0aa1-b0c6-4fef-3a5b-02f57e136b40',
    'seller-auth-erp-url': 'https%3A%2F%2Fmuke.lingxing.com%2Fapi%2Fseller%2FoauthRedirect',
    'Hm_lvt_49f9312a5d99eba61237ede945a266af': '1750903226,1751273194,1751610767,1751710786',
    'sensorsdata2015jssdkcross': '%7B%22distinct_id%22%3A%********-1%22%2C%22first_id%22%3A%22197a9f6f64193c-0ecccccccccccd-********-2073600-197a9f6f642ed5%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3YTlmNmY2NDE5M2MtMGVjY2NjY2NjY2NjY2QtMjYwMTExNTEtMjA3MzYwMC0xOTdhOWY2ZjY0MmVkNSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjkwMTIwNi0xIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%********-1%22%7D%2C%22%24device_id%22%3A%22197a9f6f64193c-0ecccccccccccd-********-2073600-197a9f6f642ed5%22%7D',
    'isNeedReset': '0',
    'isUpdatePwd': '0',
    'sensor-distinace-id': '901206-1',
    'Hm_lvt_e1b07b01489084694814b73e755122ea': '**********,**********,**********,**********',
    'HMACCOUNT': '8495F32F2DFDA3D7',
    'company_id': '901122764666708480',
    'envKey': 'muke',
    'env_key': 'muke',
    'authToken': '73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ',
    'auth-token': '73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ',
    'uid': '********',
    'isLogin': 'true',
    'zid': '1',
    'info': '%7B%22uid%22%3A%**********%22%2C%22zid%22%3A%221%22%2C%22username%22%3A%22yxyjs04%22%2C%22siteUsername%22%3A%22%22%2C%22realname%22%3A%22%E8%B4%BE%E4%B8%96%E9%BE%99%22%2C%22mobile%22%3A%22%22%2C%22nationCode%22%3A%2286%22%2C%22adminNationCode%22%3A%22%22%2C%22mealInfo%22%3A%7B%22recharge_num%22%3A0%7D%2C%22loginGuide%22%3Afalse%2C%22loginEnv%22%3A1%2C%22isPartner%22%3A0%2C%22email%22%3A%22%22%2C%22sysSubAdminFlag%22%3A0%2C%22editFlag%22%3A1%2C%22is_mobile_verified%22%3A0%2C%22is_master%22%3A0%2C%22is_email_verified%22%3A0%2C%22hide_init_guide%22%3A0%2C%22mp_hide_init_guide%22%3A0%2C%22has_bind_oauth_center%22%3A0%2C%22has_bind_jst%22%3A0%2C%22feature_info%22%3A%7B%7D%2C%22customer_id%22%3A%********%22%2C%22show_zid%22%3A%********%22%2C%22available_env%22%3A%5B%22amazon%22%2C%22multi%22%5D%2C%22api_info%22%3A%5B%5D%7D',
    'is_sellerAuth': '1',
    'token': '73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ',
    'udesk_info_901122764666708480': '%7B%22level%22%3A%22S%22%2C%22klevel%22%3A%22%E5%90%A6%22%2C%22company_id%22%3A%22901122764666708480%22%2C%22customer_id%22%3A%********%22%2C%22cs_group%22%3A%22CSG1-001%22%7D',
    'Hm_lpvt_e1b07b01489084694814b73e755122ea': '1754960571',
}

headers = {
    'AK-Client-Type': 'web',
    'AK-Origin': 'https://muke.lingxing.com',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9',
    'Connection': 'keep-alive',
    'Referer': 'https://muke.lingxing.com/erp/productExpressionNew',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-origin',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    'X-AK-Company-Id': '901122764666708480',
    'X-AK-ENV-KEY': 'muke',
    'X-AK-Language': 'zh',
    'X-AK-PLATFORM': '1',
    'X-AK-Request-Id': '456da1cb-19d7-4e75-86df-6dbfc4198912',
    'X-AK-Request-Source': 'erp',
    'X-AK-Uid': '********',
    'X-AK-Version': '3.6.6.3.0.002',
    'X-AK-Zid': '1',
    'auth-token': '73a5M6VUCGRJo5UlDAuj6WnGZ+ytDx5NkIu45c6NkLT8e9+1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR/tuERGomh2Exo6PK+M7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ',
    'baggage': 'sentry-environment=production,sentry-release=3.6.6.3,sentry-public_key=28008da1850ea166e3c367cfa8a606fa,sentry-trace_id=ee4f351fdaee4ffa97e72479a2220120,sentry-transaction=%2FproductExpressionNew,sentry-sampled=false,sentry-sample_rand=0.8475342913485101,sentry-sample_rate=0',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sentry-trace': 'ee4f351fdaee4ffa97e72479a2220120-b1691ae694f743ac-0',
    # 'Cookie': 'sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; _ga=GA1.1.1213731967.1750903226; _uetvid=53f09740523111f086f3674ff56e4186; _clck=hw1dfp%7C2%7Cfx3%7C0%7C2003; _gcl_au=1.1.1316856398.1750903228; _ga_57W1QW8BJG=GS2.1.s1750903225$o1$g0$t1750903337$j60$l0$h21324942; __wpkreporterwid_=be6f0aa1-b0c6-4fef-3a5b-02f57e136b40; seller-auth-erp-url=https%3A%2F%2Fmuke.lingxing.com%2Fapi%2Fseller%2FoauthRedirect; Hm_lvt_49f9312a5d99eba61237ede945a266af=1750903226,1751273194,1751610767,1751710786; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%********-1%22%2C%22first_id%22%3A%22197a9f6f64193c-0ecccccccccccd-********-2073600-197a9f6f642ed5%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3YTlmNmY2NDE5M2MtMGVjY2NjY2NjY2NjY2QtMjYwMTExNTEtMjA3MzYwMC0xOTdhOWY2ZjY0MmVkNSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjkwMTIwNi0xIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%********-1%22%7D%2C%22%24device_id%22%3A%22197a9f6f64193c-0ecccccccccccd-********-2073600-197a9f6f642ed5%22%7D; isNeedReset=0; isUpdatePwd=0; sensor-distinace-id=901206-1; Hm_lvt_e1b07b01489084694814b73e755122ea=**********,**********,**********,**********; HMACCOUNT=8495F32F2DFDA3D7; company_id=901122764666708480; envKey=muke; env_key=muke; authToken=73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ; auth-token=73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ; uid=********; isLogin=true; zid=1; info=%7B%22uid%22%3A%**********%22%2C%22zid%22%3A%221%22%2C%22username%22%3A%22yxyjs04%22%2C%22siteUsername%22%3A%22%22%2C%22realname%22%3A%22%E8%B4%BE%E4%B8%96%E9%BE%99%22%2C%22mobile%22%3A%22%22%2C%22nationCode%22%3A%2286%22%2C%22adminNationCode%22%3A%22%22%2C%22mealInfo%22%3A%7B%22recharge_num%22%3A0%7D%2C%22loginGuide%22%3Afalse%2C%22loginEnv%22%3A1%2C%22isPartner%22%3A0%2C%22email%22%3A%22%22%2C%22sysSubAdminFlag%22%3A0%2C%22editFlag%22%3A1%2C%22is_mobile_verified%22%3A0%2C%22is_master%22%3A0%2C%22is_email_verified%22%3A0%2C%22hide_init_guide%22%3A0%2C%22mp_hide_init_guide%22%3A0%2C%22has_bind_oauth_center%22%3A0%2C%22has_bind_jst%22%3A0%2C%22feature_info%22%3A%7B%7D%2C%22customer_id%22%3A%********%22%2C%22show_zid%22%3A%********%22%2C%22available_env%22%3A%5B%22amazon%22%2C%22multi%22%5D%2C%22api_info%22%3A%5B%5D%7D; is_sellerAuth=1; token=73a5M6VUCGRJo5UlDAuj6WnGZ%2BytDx5NkIu45c6NkLT8e9%2B1X4UfSlBAW3xIVPc52ntOWz2e0r0CgMqlo3OQks7pgNpppR%2FtuERGomh2Exo6PK%2BM7m9eWUZwDQNsBQwRm27MynCBCdNqv37F2dulvQ; udesk_info_901122764666708480=%7B%22level%22%3A%22S%22%2C%22klevel%22%3A%22%E5%90%A6%22%2C%22company_id%22%3A%22901122764666708480%22%2C%22customer_id%22%3A%********%22%2C%22cs_group%22%3A%22CSG1-001%22%7D; Hm_lpvt_e1b07b01489084694814b73e755122ea=1754960571',
}

response = requests.get(
    'https://muke.lingxing.com/api/my/sellers?req_time_sequence=%2Fapi%2Fmy%2Fsellers$$1',
    cookies=cookies,
    headers=headers,
)


// 返回结果示例：
{
    "code": 1,
    "msg": "操作成功",
    "require_id": "27EAEE62-698F-4C2A-35E1-A5DB54489858",
    "list": [
        {
            "id": 1592,
            "name": "AM_ACH-BE",
            "seller_id": "A2RAAMY2D47TKE",
            "country_code": "BE",
            "country": "比利时",
            "is_concept": 0
        },
        {
            "id": 1586,
            "name": "AM_ACH-DE",
            "seller_id": "A2RAAMY2D47TKE",
            "country_code": "DE",
            "country": "德国",
            "is_concept": 0
        },
        {
            "id": 1588,
            "name": "AM_ACH-ES",
            "seller_id": "A2RAAMY2D47TKE",
            "country_code": "ES",
            "country": "西班牙",
            "is_concept": 0
        },
        {
            "id": 1587,
            "name": "AM_ACH-FR",
            "seller_id": "A2RAAMY2D47TKE",
            "country_code": "FR",
            "country": "法国",
            "is_concept": 0
        },



purchase_status
: 
0
query_order_profit
: 
true
regions
: 
[]
req_time_sequence
: 
"/bd/productPerformance/downloadValidate$$1"
search_field
: 
"asin"
search_value
: 
[]
sids
: 
"1592,845,1586,1588,1587,1585,1589,1591,1590,2982,1584,3853,3847,3849,3848,3854,3846,3850,3852,3851,3845,1420,1414,1416,1415,3759,1413,1417,1419,1418,2833,1412,1571,1565,1567,1566,3663,1564,1568,1570,1569,1563,1438,1432,1434,1433,1431,1435,1437,1436,1430,3684,3685,3683,3633,3627,3629,3628,3634,3626,3630,3632,3631,3625,3086,3080,3082,3081,3079,3083,3085,3084,3078,3279,3273,3275,3274,3272,3276,3278,3277,3271,2676,2670,2672,2671,3697,2669,2673,2675,2674,2668,947,133,130,134,131,132,135,136,946,129,1876,1870,1872,1871,1869,1873,1875,1874,3026,1868,1496,1490,1492,1491,1489,1493,1495,1494,1488,2564,2565,2563,2782,695,697,696,694,698,700,699,693,1475,1477,1476,1474,1478,1480,1479,1473,1481,846,840,842,841,839,843"
sort_field
: 
"volume"
sort_type
: 
"desc"
start_date
: 
"2025-08-11"
sub_summary_type
: 
""
summary_field
: 
"asin"
summary_field_level1
: 
""





CREATE TABLE `data_lingxing_count_goods` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `tenant_id` int(11) NOT NULL DEFAULT '0' COMMENT '租户ID',
  `company_id` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '公司ID',
  `unique_id` varchar(100) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `app_id` int(11) NOT NULL DEFAULT '0' COMMENT '应用ID',
  `task_id` int(11) NOT NULL DEFAULT '0' COMMENT '任务ID',
  `datetime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '所属时间',
  `task_time` int(11) NOT NULL DEFAULT '0' COMMENT '任务时间',
  `data_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '数据状态 1正常2异常3无数据',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户',
  `username` varchar(40) NOT NULL DEFAULT '' COMMENT '用户名称',
  `parent_asin` varchar(16) NOT NULL DEFAULT '' COMMENT '父ASIN',
  `asin` varchar(16) NOT NULL DEFAULT '' COMMENT 'ASIN',
  `msku` varchar(64) NOT NULL DEFAULT '' COMMENT 'MSKU',
  `local_name` varchar(64) NOT NULL DEFAULT '' COMMENT '品名',
  `model` varchar(4) NOT NULL DEFAULT '' COMMENT '型号',
  `local_sku` varchar(32) NOT NULL DEFAULT '' COMMENT 'SKU',
  `spu_name` varchar(16) NOT NULL DEFAULT '' COMMENT '款名',
  `spu` varchar(16) NOT NULL DEFAULT '' COMMENT 'SPU',
  `tags` varchar(32) NOT NULL DEFAULT '' COMMENT 'listing标签',
  `item_name` varchar(256) NOT NULL DEFAULT '' COMMENT '标题',
  `mid` varchar(4) NOT NULL DEFAULT '' COMMENT '国家',
  `sid` varchar(16) NOT NULL DEFAULT '' COMMENT '店铺',
  `bid` varchar(16) NOT NULL DEFAULT '' COMMENT '品牌',
  `cid` varchar(16) NOT NULL DEFAULT '' COMMENT '一级分类',
  `cid2` varchar(16) NOT NULL DEFAULT '' COMMENT '二级分类',
  `cid3` varchar(16) NOT NULL DEFAULT '' COMMENT '三级分类',
  `principal_names` varchar(16) NOT NULL DEFAULT '' COMMENT '负责人',
  `developer_names` varchar(16) NOT NULL DEFAULT '' COMMENT '开发人',
  `product_create_time` varchar(32) NOT NULL DEFAULT '' COMMENT '创建时间',
  `landed_price` varchar(8) NOT NULL DEFAULT '' COMMENT '售价(总价)',
  `currency` varchar(8) NOT NULL DEFAULT '' COMMENT '币种',
  `volume` int(11) NOT NULL COMMENT '销量',
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售额',
  `order_items` int(11) NOT NULL COMMENT '订单量',
  `volume_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量环比',
  `amount_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量额环比',
  `order_chain_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '订单量环比',
  `volume_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量同比',
  `amount_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '销量额同比',
  `order_yoy_ratio` varchar(8) NOT NULL DEFAULT '' COMMENT '订单量同比',
  `net_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '净销售额',
  `b2b_volume` int(11) NOT NULL COMMENT 'B2B 销量',
  `b2b_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'B2B 销售额',
  `b2b_order_items` int(11) NOT NULL COMMENT 'B2B 订单量',
  `promotion_volume` int(11) NOT NULL COMMENT '促销销量',
  `promotion_amount` int(11) NOT NULL COMMENT '促销销售额',
  `promotion_order_items` int(11) NOT NULL COMMENT '促销订单量',
  `avg_custom_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售均价',
  `avg_volume` int(11) NOT NULL COMMENT '平均销量',
  `promotion_discount` int(11) NOT NULL COMMENT '促销折扣',
  `fbm_buyer_expenses` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'FBM买家运费',
  `cate_rank` varchar(64) NOT NULL DEFAULT '' COMMENT '大类排名',
  `small_cate_rank` varchar(128) NOT NULL DEFAULT '' COMMENT '小类排名',
  `return_count` int(11) NOT NULL COMMENT '退款量',
  `return_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `return_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '退款率',
  `avg_star` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '评分',
  `reviews_count` int(11) NOT NULL COMMENT '评论数',
  `gross_profit` varchar(2) NOT NULL DEFAULT '' COMMENT '结算毛利润',
  `predict_gross_profit` varchar(4) NOT NULL DEFAULT '' COMMENT '订单毛利润',
  `gross_margin` varchar(2) NOT NULL DEFAULT '' COMMENT '结算毛利率',
  `predict_gross_margin` varchar(4) NOT NULL DEFAULT '' COMMENT '订单毛利率',
  `roi` varchar(2) NOT NULL DEFAULT '' COMMENT 'ROI',
  `return_goods_count` int(11) NOT NULL COMMENT '退货量',
  `return_goods_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '退货率',
  `afn_fulfillable_quantity` int(11) NOT NULL COMMENT 'FBA-可售',
  `available_inventory` int(11) NOT NULL COMMENT '可用库存',
  `fbm_quantity` int(11) NOT NULL COMMENT 'FBM可售',
  `available_days` int(11) NOT NULL COMMENT 'FBA可售天数预估',
  `fbm_available_days` int(11) NOT NULL COMMENT 'FBM可售天数预估',
  `oversea_quantity` int(11) NOT NULL COMMENT '海外仓可用',
  `local_quantity` int(11) NOT NULL COMMENT '本地可用',
  `purchase_num` int(11) NOT NULL COMMENT '采购量',
  `month_stock_sales_ratio` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '月库销比',
  `out_stock_date` varchar(16) NOT NULL DEFAULT '' COMMENT '断货时间',
  `reserved_fc_transfers` int(11) NOT NULL COMMENT 'FBA-待调仓',
  `reserved_fc_processing` int(11) NOT NULL COMMENT 'FBA-调仓中',
  `afn_inbound_receiving_quantity` int(11) NOT NULL COMMENT 'FBA-入库中',
  `afn_total_inbound` int(11) NOT NULL COMMENT 'FBA-小计',
  `reserved_customerorders` int(11) NOT NULL COMMENT 'FBA-待发货',
  `afn_inbound_shipped_quantity` int(11) NOT NULL COMMENT 'FBA-在途',
  `afn_inbound_working_quantity` int(11) NOT NULL COMMENT 'FBA-计划入库',
  `afn_unsellable_quantity` int(11) NOT NULL COMMENT 'FBA-不可售',
  `cpc` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPC',
  `ctr` varchar(8) NOT NULL DEFAULT '' COMMENT 'CTR',
  `spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告花费',
  `sb_spend` int(11) NOT NULL COMMENT 'SB广告费',
  `sbv_spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'SBV广告费',
  `sd_spend` int(11) NOT NULL COMMENT 'SD广告费',
  `sp_spend` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'SP广告费',
  `roas` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'ROAS',
  `acos` varchar(8) NOT NULL DEFAULT '' COMMENT 'ACOS',
  `acoas` varchar(8) NOT NULL DEFAULT '' COMMENT 'ACoAS',
  `asoas` varchar(8) NOT NULL DEFAULT '' COMMENT 'ASoAS',
  `cpo` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPO',
  `cpu` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPU',
  `cpm` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'CPM',
  `ad_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '广告销售额',
  `ad_order_quantity` int(11) NOT NULL COMMENT '广告订单量',
  `ad_direct_order_quantity` int(11) NOT NULL COMMENT '直接成交订单量',
  `ad_direct_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '直接成交销售额',
  `adv_rate` varchar(8) NOT NULL DEFAULT '' COMMENT '广告订单占比',
  `ad_cvr` varchar(8) NOT NULL DEFAULT '' COMMENT '广告CVR',
  `impressions` int(11) NOT NULL COMMENT '展示',
  `clicks` int(11) NOT NULL COMMENT '点击',
  `create_time` int(11) NOT NULL DEFAULT '0' COMMENT ' 创建时间 ',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ' 更新时间 ',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `unique_id` (`unique_id`) USING BTREE COMMENT ' 唯一ID ',
  KEY `datetime` (`datetime`)
) ENGINE=InnoDB AUTO_INCREMENT=395975 DEFAULT CHARSET=utf8mb4 COMMENT=' 领星统计数据-产品表现 ';